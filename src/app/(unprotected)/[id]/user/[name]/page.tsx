import apiClient from "@/services/api-client";
import { isAxiosError } from "axios";
import PublicProfile from "./PublicProfile";

// Define props type
type Props = {
  params: {
    id: string;
    name: string;
  };
};

// Server-side data fetching for profile
async function getProfileData(userId: string) {
  try {
    const response = await apiClient.get(`/roles/profile/${userId}`);
    if (response.status === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error("Error fetching profile data:", error);
    return null;
  }
}

// Server-side data fetching for categories
async function getCategoriesData(userId: string) {
  try {
    console.log(`Fetching categories for user ${userId}`);
    const categoriesResponse = await apiClient.get(`/roles/profile/${userId}/categories`);
    console.log("Categories response:", categoriesResponse.data);
    return categoriesResponse.data;
  } catch (error) {
    if (isAxiosError(error)) {
      console.error("Error fetching categories data:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
        },
      });
    } else {
      console.error("Error fetching categories data:", error);
    }
    return [];
  }
}

// Generate metadata for SEO
// export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
//   const { id, name } = params;

//   // Fetch profile data for metadata
//   const profile = await getProfileData(id);

//   // Get parent metadata
//   const previousMetadata = await parent;

//   // Generate SEO-friendly title and description
//   const title = profile?.title || `${profile?.first_name || ""} ${profile?.middle_name || ""} ${profile?.last_name || ""}`.trim() || name || "Public Profile";

//   const description = profile?.bio || `View ${title}'s professional profile, specialities${profile?.speciality ? ` in ${profile?.speciality}` : ""}, and more.`;

//   return {
//     title,
//     description,
//     openGraph: {
//       title,
//       description,
//       images: profile?.profile_picture ? [profile.profile_picture] : [],
//       type: "profile",
//     },
//     twitter: {
//       card: "summary_large_image",
//       title,
//       description,
//       images: profile?.profile_picture ? [profile.profile_picture] : [],
//     },
//   };
// }

export default async function Page({ params }: any) {
  const { id, name } = await params;

  // Fetch data server-side
  const profile = await getProfileData(id);
  const categories = await getCategoriesData(id);

  // Pass data to client component
  return (
    <PublicProfile
      isVerified={profile?.is_id_verified}
      hasVerifiedProfile={profile?.paid_for_verification}
      id={id}
      name={name}
      initialProfile={profile}
      initialCategories={categories}
    />
  );
}
