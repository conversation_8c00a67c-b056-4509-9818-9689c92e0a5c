import AIModelComparison from "@/components/AIModelComparison";
import ServiceCard from "@/components/Card/ServiceCard";
import ChatInput from "@/components/ChatInput";
import ImportantNoticeDropdown from "@/components/Disclaimers/ImportantNoticeDropdown";
import TermsOfUse from "@/components/Disclaimers/TermsOfUse";
import TabContainer from "@/components/Tabs/TabComponent";

const Home = () => {
  return (
    <div className="flex flex-col gap-2 p-2">
      <ImportantNoticeDropdown />
      <TermsOfUse />
      <ChatInput />
      <AIModelComparison />
      <div className="flex flex-col gap-2 text-center mt-3">
        <h2 className="mb-2">AI Analysis Enabling Options</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <ServiceCard
            serviceInfo={{
              title: "AI Agent Integration Monthly Subscription",
              benefits: ["Full monthly access to AI Analysis", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
            }}
            priceInfo={{
              price: 20,
              priceUnit: "Month",
            }}
            onClick={() => {}}
            buttonText="Select"
          />
          <ServiceCard
            serviceInfo={{
              title: "One Day Payment",
              benefits: ["AI Analysis of a single day (24 hours)", "Applicable throughout the R.A.V.I.D. platform", "Daily Token Limits for AI Agent"],
            }}
            priceInfo={{
              price: 1,
              priceUnit: "Day",
            }}
            onClick={() => {}}
            buttonText="Select"
          />
        </div>
      </div>
    </div>
  );
};

export default Home;
