"use client";
import React, { useState, useRef, useEffect } from "react";
import { useStartChat, useSendChatMessage, useGetChatById, useSendUnifiedMessage } from "@/hooks/home-dashboard/useAI";
import { <PERSON><PERSON>, <PERSON>, cn } from "@heroui/react";
import { Textarea } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { useGetUser } from "@/hooks/useUser";
import { ArrowUp, History as HistoryIcon, Image as ImageIcon, Mic, Plus, Send, X } from "lucide-react";
import { RsIcon } from "./icons";
import History from "./History";
import CopyComponent from "./Copy";
import DownloadAsPdf from "./DownloadAsPdf";
import toast from "react-hot-toast";

const STORAGE_KEY = "chat_state";

// Add TypeScript definitions for Speech Recognition
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
  error: any;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  isFinal: boolean;
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionEvent) => void;
  onend: () => void;
  onstart: () => void;
  start(): void;
  stop(): void;
  abort(): void;
  new (): SpeechRecognition;
}

declare global {
  interface Window {
    SpeechRecognition: {
      new (): SpeechRecognition;
      prototype: SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new (): SpeechRecognition;
      prototype: SpeechRecognition;
    };
  }
}

type Message = {
  role: "user" | "assistant";
  content: string;
  isStreaming?: boolean;
  displayContent?: string;
};

const ChatInput = () => {
  // Load initial state from localStorage
  const { data: user } = useGetUser();
  const loadInitialState = () => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        return {
          initialMessages: parsed.messages || [],
          initialModel: parsed.selectedModel || "gemini",
          initialChatId: parsed.chatId || null,
        };
      }
    } catch (error) {
      console.error("Error loading chat state:", error);
    }
    return {
      initialMessages: [],
      initialModel: "gemini",
      initialChatId: null,
    };
  };

  const { initialMessages, initialModel, initialChatId } = loadInitialState();
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputMessage, setInputMessage] = useState("");
  const [selectedModel, setSelectedModel] = useState(initialModel);
  const [chatId, setChatId] = useState<string | null>(initialChatId);
  const [userScrolled, setUserScrolled] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isRavidSearchActive, setIsRavidSearchActive] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const streamingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [processingMethod, setProcessingMethod] = useState<"multimodal" | "text_extraction">("multimodal");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { mutate: startChat, isPending: isStarting } = useStartChat();
  const { mutate: sendMessage, isPending: isSending } = useSendChatMessage();
  const { mutate: getChatById, isPending: isLoadingChat } = useGetChatById();
  const { mutate: sendUnifiedMessage } = useSendUnifiedMessage();

  // Save chat state to localStorage whenever it changes
  useEffect(() => {
    try {
      if (messages.length > 0 || chatId) {
        const stateToSave = {
          messages,
          selectedModel,
          chatId,
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
        console.log("Saved chat state:", stateToSave);
      }
    } catch (error) {
      console.error("Error saving chat state:", error);
    }
  }, [messages, selectedModel, chatId]);

  const scrollToBottom = () => {
    if (chatContainerRef.current && !userScrolled) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  const activateRavidSearch = () => {
    setIsRavidSearchActive(!isRavidSearchActive);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isScrolledToBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 10;
      setUserScrolled(!isScrolledToBottom);
    }
  };

  const resetChat = () => {
    setMessages([]);
    setChatId(null);
    setInputMessage("");
    setUserScrolled(false);
    localStorage.removeItem(STORAGE_KEY);
  };

  const formatResponse = (content: string) => {
    let formatted = content
      .trim()
      .replace(/^```json\n/, "")
      .replace(/```$/, "")
      .replace(/\\n/g, "<br/>")
      .replace(/\*\*(\d+\.)\s*([^:*]+):\*\*/g, '<strong class="block mt-2">$1 $2:</strong>')
      .replace(/\*\*([^*]+?):\*\*/g, "<strong>$1:</strong>")
      .replace(/\*\*([^*]+?)\*\*/g, "<strong>$1</strong>")
      .replace(/\n/g, "<br/>")
      .replace(/_([^_]+)_/g, "<em>$1</em>")
      .replace(/`([^`]+)`/g, "<code class='bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-xs'>$1</code>")
      .replace(/\*\s*(.*?)(?:<br\/>|$)/g, '<li class="ml-4">$1</li>')
      .replace(/(<li.*?>.*?<\/li>)+/g, '<ul class="list-disc space-y-1 mt-2">$&</ul>');

    return formatted;
  };

  const toggleHistory = () => {
    setIsHistoryOpen(!isHistoryOpen);
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      role: "user",
      content: inputMessage + (selectedFiles.length > 0 ? ` (${selectedFiles.length} file(s) attached)` : ""),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");

    if (!chatId) {
      // Start new chat
      startChat(
        {
          message: inputMessage,
          model: selectedModel,
          enable_search: isRavidSearchActive,
        },
        {
          onSuccess: (response: any) => {
            try {
              const jsonMatch = response.match(/^\s*({.*?})/);
              if (jsonMatch) {
                const jsonPart = JSON.parse(jsonMatch[1]);
                const newChatId = jsonPart.chat_id;
                setChatId(newChatId);

                // If we have files, send them after chat is created
                if (selectedFiles.length > 0) {
                  sendUnifiedMessage(
                    {
                      message: inputMessage,
                      chat_id: newChatId,
                      files: selectedFiles,
                      processing_method: processingMethod,
                    },
                    {
                      onSuccess: (data) => {
                        try {
                          const responseText = data.toString();
                          const parts = responseText.split("\n");
                          const jsonLine = parts[0];
                          const jsonResponse = JSON.parse(jsonLine);
                          const textContent = parts
                            .slice(1)
                            .join("\n")
                            .trim();

                          if (jsonResponse?.chat_id) {
                            setChatId(jsonResponse.chat_id);
                            const sanitizedContent = formatResponse(textContent);
                            setMessages((prev) => {
                              const newMessages = [
                                ...prev,
                                {
                                  role: "assistant" as const,
                                  content: sanitizedContent,
                                  isStreaming: true,
                                  displayContent: "",
                                },
                              ];
                              simulateStreaming(newMessages.length - 1, sanitizedContent);
                              return newMessages;
                            });
                          }
                        } catch (error) {
                          console.error("Failed to parse JSON:", error);
                          toast.error("Failed to process the response");
                        }
                        setSelectedFiles([]);
                      },
                    }
                  );
                } else {
                  // Add assistant response to chat
                  const messageContent = response.replace(/^\s*({.*?})/, "").trim();
                  const formattedContent = formatResponse(messageContent);
                  setMessages((prev) => {
                    const newMessages = [
                      ...prev,
                      {
                        role: "assistant" as const,
                        content: formattedContent,
                        isStreaming: true,
                        displayContent: "",
                      },
                    ];
                    simulateStreaming(newMessages.length - 1, formattedContent);
                    return newMessages;
                  });
                }
              }
            } catch (error) {
              console.error("Error processing response:", error);
            }
          },
        }
      );
    } else {
      // Continue existing chat
      if (selectedFiles.length > 0) {
        sendUnifiedMessage(
          {
            message: inputMessage,
            chat_id: chatId,
            files: selectedFiles,
            processing_method: processingMethod,
          },
          {
            onSuccess: (data) => {
              try {
                const responseText = data.toString();
                const parts = responseText.split("\n");
                const jsonLine = parts[0];
                const jsonResponse = JSON.parse(jsonLine);
                const textContent = parts
                  .slice(1)
                  .join("\n")
                  .trim();

                if (jsonResponse?.chat_id) {
                  setChatId(jsonResponse.chat_id);
                  const sanitizedContent = formatResponse(textContent);
                  setMessages((prev) => {
                    const newMessages = [
                      ...prev,
                      {
                        role: "assistant" as const,
                        content: sanitizedContent,
                        isStreaming: true,
                        displayContent: "",
                      },
                    ];
                    simulateStreaming(newMessages.length - 1, sanitizedContent);
                    return newMessages;
                  });
                }
              } catch (error) {
                console.error("Failed to parse JSON:", error);
                toast.error("Failed to process the response");
              }
              setSelectedFiles([]);
            },
          }
        );
      } else {
        sendMessage(
          {
            message: inputMessage,
            model: selectedModel,
            chat_id: chatId,
            enable_search: isRavidSearchActive,
          },
          {
            onSuccess: (response) => {
              try {
                const jsonMatch = response.match(/^\s*({.*?})/);
                if (jsonMatch) {
                  const jsonPart = JSON.parse(jsonMatch[1]);
                  setChatId(jsonPart.chat_id);
                }
                const messageContent = response.replace(/^\s*({.*?})/, "").trim();
                const formattedContent = formatResponse(messageContent);
                setMessages((prev) => {
                  const newMessages = [
                    ...prev,
                    {
                      role: "assistant" as const,
                      content: formattedContent,
                      isStreaming: true,
                      displayContent: "",
                    },
                  ];
                  simulateStreaming(newMessages.length - 1, formattedContent);
                  return newMessages;
                });
              } catch (error) {
                console.error("Error processing response:", error);
              }
            },
          }
        );
      }
    }
  };

  // Add new state variables for voice input
  const [isListening, setIsListening] = useState<boolean>(false);
  const [hasRecognitionSupport, setHasRecognitionSupport] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const isBrave = useRef<boolean>(false);

  // Check browser compatibility
  useEffect(() => {
    isBrave.current = "brave" in navigator;
    if ("SpeechRecognition" in window || "webkitSpeechRecognition" in window) {
      setHasRecognitionSupport(true);
    }
  }, []);

  const startListening = () => {
    if (!hasRecognitionSupport) {
      toast.error("Your browser doesn't support speech recognition.");
      return;
    }

    if (isBrave.current) {
      toast.error("Speech recognition doesn't work in Brave browser due to privacy restrictions. Please use Chrome or Edge.");
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();

    if (recognitionRef.current) {
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = "en-US";

      recognitionRef.current.onstart = () => {
        setIsListening(true);
      };

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = Array.from(event.results)
          .map((result) => result[0])
          .map((result) => result.transcript)
          .join("");

        setInputMessage(transcript);
      };

      recognitionRef.current.onerror = (event: SpeechRecognitionEvent) => {
        switch (event.error) {
          case "network":
            toast.error("Network error. Please check your internet connection.");
            break;
          case "not-allowed":
          case "permission-denied":
            toast.error("Microphone access denied. Please allow microphone access in your browser settings.");
            break;
          case "no-speech":
            toast.error("No speech detected. Please try speaking more clearly.");
            break;
          case "audio-capture":
            toast.error("No microphone detected. Please check your microphone connection.");
            break;
          default:
            toast.error(`Recognition error: ${event.error}`);
        }
        setIsListening(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };

      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error("Failed to start speech recognition:", error);
        toast.error("Failed to start speech recognition. Please try again.");
        setIsListening(false);
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error("Error stopping recognition:", error);
      }
      setIsListening(false);
    }
  };

  const handleSelectChat = (selectedChatId: string, selectedModel: string) => {
    setSelectedModel(selectedModel);
    setChatId(selectedChatId);
    setIsHistoryOpen(false);

    getChatById(
      { id: selectedChatId },
      {
        onSuccess: (response) => {
          try {
            if (!response || typeof response !== "object") {
              console.error("Invalid response format:", response);
              return;
            }

            // The response contains messages in the data property
            const messages = response.data || response.messages || [];
            if (!Array.isArray(messages)) {
              console.error("Messages is not an array:", messages);
              return;
            }

            // Format each message in the response
            const formattedMessages = messages
              .map((msg): { role: "user" | "assistant"; content: string } | null => {
                if (!msg || typeof msg !== "object" || !("role" in msg) || !("content" in msg)) {
                  console.error("Invalid message format:", msg);
                  return null;
                }

                return {
                  role: msg.role as "user" | "assistant",
                  content: msg.role === "assistant" ? formatResponse(msg.content) : msg.content,
                };
              })
              .filter((msg): msg is { role: "user" | "assistant"; content: string } => msg !== null);

            if (formattedMessages.length > 0) {
              setMessages(formattedMessages);
              // Scroll to bottom after messages are loaded
              setTimeout(() => {
                if (chatContainerRef.current) {
                  chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
                }
              }, 100);
            } else {
              console.error("No valid messages found in response");
              toast.error("No messages found in this chat");
            }
          } catch (error) {
            console.error("Error processing chat response:", error);
            toast.error("Failed to load chat history");
          }
        },
        onError: (error) => {
          console.error("Error fetching chat:", error);
          toast.error("Failed to load chat history");
        },
      }
    );
  };

  // Format chat messages for PDF download
  const formatChatForPDF = () => {
    if (messages.length === 0) return "";

    const header = `Chat Conversation\nModel: ${selectedModel}\nDate: ${new Date().toLocaleDateString()}\nTotal Messages: ${messages.length}\n\n${"=".repeat(
      50
    )}\n\n`;

    const formattedMessages = messages
      .map((message, index) => {
        const role = message.role === "user" ? "You" : "Assistant";
        const content =
          message.role === "assistant"
            ? message.content.replace(/<[^>]*>/g, "").replace(/&nbsp;/g, " ") // Strip HTML tags
            : message.content;

        return `${role}:\n${content}\n${"-".repeat(30)}\n`;
      })
      .join("\n");

    return header + formattedMessages + `\nGenerated on: ${new Date().toLocaleString()}`;
  };

  // Add streaming simulation function
  const simulateStreaming = (messageIndex: number, fullContent: string) => {
    let currentIndex = 0;

    const streamInterval = setInterval(() => {
      if (currentIndex <= fullContent.length) {
        setMessages((prevMessages) => {
          const newMessages = [...prevMessages];
          if (newMessages[messageIndex]) {
            newMessages[messageIndex] = {
              ...newMessages[messageIndex],
              displayContent: fullContent.slice(0, currentIndex),
              isStreaming: currentIndex < fullContent.length,
            };
          }
          return newMessages;
        });
        currentIndex += 3; // Adjust speed by changing this number
      } else {
        if (streamingIntervalRef.current) {
          clearInterval(streamingIntervalRef.current);
          streamingIntervalRef.current = null;
        }
      }
    }, 10);

    streamingIntervalRef.current = streamInterval;
  };

  // Clean up streaming interval on unmount
  useEffect(() => {
    return () => {
      if (streamingIntervalRef.current) {
        clearInterval(streamingIntervalRef.current);
      }
    };
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedFiles(files);
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };


// Function to clean HTML tags from text
const cleanHtmlTags = (html: string): string => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  return doc.body.textContent || "";
};

  return (
    <div className="flex flex-col gap-4 w-full mx-auto border rounded-lg dark:border-gray-800 relative">
      {/* Chat Header */}
      <div className="flex justify-between items-center p-1 md:p-3 border-b dark:border-gray-800">
        <Button size="sm" variant="flat" color="default" onPress={resetChat} isDisabled={isStarting || isSending || isLoadingChat}>
          <Plus className="w-4 h-4" />
          New Chat
        </Button>
        <Button size="sm" variant="flat" color="default" onPress={toggleHistory} className={cn(isHistoryOpen && "bg-gray-100 dark:bg-slate-800")}>
          <HistoryIcon className="w-4 h-4" />
          History
        </Button>
      </div>

      {/* History Component */}
      <History isOpen={isHistoryOpen} onClose={toggleHistory} currentChatId={chatId} onSelectChat={handleSelectChat} />

      {/* Chat Messages */}
      <Card
        ref={chatContainerRef}
        onScroll={handleScroll}
        className="flex flex-col gap-4 min-h-[100px] max-h-[325px] overflow-y-auto p-2 md:p-4 rounded-lg dark:bg-slate-950 mx-1 md:mx-3"
      >
        {isLoadingChat ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500 dark:text-gray-400">Loading chat...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-600">
              Hello, {user?.first_name ? user?.first_name : user?.email}
            </span>
            <p className="text-gray-500 dark:text-gray-400 text-center">How can I help you today?</p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              <div
                className={`max-w-[70%] p-2 md:p-3 rounded-lg md:rounded-xl relative ${
                  message.role === "user"
                    ? "dark:bg-slate-900 bg-gray-900 md:bg-slate-900 text-xs md:text-sm text-white ml-auto"
                    : "bg-gray-100 dark:bg-slate-900 mr-auto"
                }`}
              >
                {message.role === "user" ? (
                  <p className="text-xs md:text-sm whitespace-pre-wrap">{message.content}</p>
                ) : (
                  <>
                    <div className="absolute top-2 right-2 flex gap-1">
                      <CopyComponent text={cleanHtmlTags(message.content)} />
                    </div>
                    <div className="text-xs md:text-sm prose dark:prose-invert max-w-none pr-16">
                      <div dangerouslySetInnerHTML={{ __html: message.displayContent || message.content }} />
                      {message.isStreaming && <span className="inline-block w-1 h-4 ml-1 bg-gray-500 animate-pulse"></span>}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        )}
      </Card>

      {/* Mobile Modern Input Area - Only visible on mobile */}
      <div className="block md:hidden px-3 pb-3">
        {/* File Preview */}
        {selectedFiles.length > 0 && (
          <div className="flex flex-wrap gap-2 p-3 mb-3 bg-gray-50 dark:bg-slate-900 rounded-lg border-[0.5px] dark:border-gray-800">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center gap-2 bg-white dark:bg-slate-800 p-2 rounded-md border-[0.5px] dark:border-gray-700">
                <span className="text-sm truncate max-w-[120px]">{file.name}</span>
                <button onClick={() => handleRemoveFile(index)} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Advanced Controls Row */}
        <div className="flex items-center justify-between mb-3 px-1">
          <div className="flex items-center gap-2">
            <Select
              classNames={{
                trigger: "dark:bg-slate-900 bg-gray-50 border-[0.5px] border-gray-200 dark:border-gray-700 h-8",
                value: "text-xs",
                innerWrapper: "text-xs",
              }}
              variant="bordered"
              aria-label="Select Model"
              selectedKeys={[selectedModel]}
              className="w-[110px]"
              onChange={(e) => setSelectedModel(e.target.value)}
              size="sm"
            >
              <SelectItem key="gemini">✨ Gemini</SelectItem>
              <SelectItem key="anthropic">🧠 Claude</SelectItem>
            </Select>

            {selectedFiles.length > 0 && (
              <Select
                classNames={{
                  trigger: "dark:bg-slate-900 bg-gray-50 border-[0.5px] border-gray-200 dark:border-gray-700 h-9",
                  value: "text-xs",
                  innerWrapper: "text-xs",
                }}
                variant="bordered"
                aria-label="Processing Method"
                selectedKeys={[processingMethod]}
                className="w-[120px]"
                onChange={(e) => setProcessingMethod(e.target.value as "multimodal" | "text_extraction")}
                size="sm"
              >
                <SelectItem key="multimodal">🖼️ Analysis</SelectItem>
                <SelectItem key="text_extraction">📄 Extract</SelectItem>
              </Select>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              className={cn(
                "px-2 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center gap-1 border-[0.5px] h-8",
                isRavidSearchActive
                  ? "dark:bg-[#1f1729] bg-[#b790ca] text-white border-[#7d5191]"
                  : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-800 bg-gray-50 dark:bg-slate-900"
              )}
              onClick={activateRavidSearch}
            >
              <RsIcon className="w-3 h-3" />
              <span>R.A.V.I.D. Search</span>
            </button>

            <DownloadAsPdf
              text={formatChatForPDF()}
              className={cn(
                "h-8 w-8 rounded-lg border-[0.5px] border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-slate-900 hover:bg-gray-100 dark:hover:bg-slate-800 p-2 transition-all duration-200",
                messages.length === 0 ? "opacity-50 cursor-not-allowed" : ""
              )}
            />
          </div>
        </div>

        {/* Main Input Container */}
        <div className="relative">
          <Textarea
            size="lg"
            minRows={1}
            maxRows={4}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            placeholder="Type your message..."
            classNames={{
              input: "text-xs pr-[110px] resize-none",
              inputWrapper: "dark:bg-slate-900 bg-gray-50 border-[0.5px] border-gray-200 dark:border-gray-700 rounded-xl",
              mainWrapper: "w-full",
              innerWrapper: "py-2 px-3",
            }}
            variant="bordered"
          />

          {/* Input Action Buttons */}
          <div className="absolute right-3 bottom-3 flex items-center gap-1">
            <input type="file" ref={fileInputRef} onChange={handleFileSelect} className="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt" />

            <button
              onClick={() => fileInputRef.current?.click()}
              className="h-7 w-7 rounded-lg p-1 transition-all duration-200 cursor-pointer text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-slate-800"
            >
              <ImageIcon className="h-full w-full" />
            </button>

            <button
              onClick={isListening ? stopListening : startListening}
              className={cn(
                "h-7 w-7 rounded-lg p-1 transition-all duration-200 cursor-pointer",
                isListening ? "text-red-500 bg-red-50 dark:bg-red-900/20" : "text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-slate-800"
              )}
            >
              <Mic className="h-full w-full" />
            </button>

            <Button
              onPress={handleSendMessage}
              isDisabled={!inputMessage.trim() || isStarting || isSending}
              size="sm"
              className={cn(
                "h-7 w-7 p-0 rounded-lg transition-all duration-200 min-w-7",
                inputMessage.trim() && !isStarting && !isSending
                  ? "bg-[#7b3304] hover:bg-[#833403] text-white"
                  : "bg-gray-200 dark:bg-slate-700 text-gray-400 cursor-not-allowed"
              )}
            >
              {isStarting || isSending ? (
                <div className="w-3 h-3 border border-gray-300 border-t-white rounded-full animate-spin" />
              ) : (
                <ArrowUp className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Original Input Area - Only visible on desktop */}
      <div className="hidden md:flex flex-col gap-2 relative border-t dark:border-gray-800 px-2">
        <div className="flex gap-2 relative justify-between items-end p-1">
          <div className="relative w-full">
            {selectedFiles.length > 0 && (
              <div className="flex flex-wrap gap-2 p-2 mb-2 bg-gray-100 dark:bg-slate-900 rounded-lg">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center gap-2 bg-white dark:bg-slate-800 p-1 rounded">
                    <span className="text-xs truncate max-w-[100px]">{file.name}</span>
                    <button onClick={() => handleRemoveFile(index)} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                      <X size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
            <Textarea
              size="sm"
              minRows={4}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              placeholder="Type your message..."
              classNames={{
                input: "text-xs pr-[160px]",
                inputWrapper: "dark:bg-slate-950",
                mainWrapper: "bg-oraneg-800",
                innerWrapper: "bg-oraneg-800",
              }}
            />
            <div className="absolute flex justify-between w-full gap-2 left-1 bottom-1">
              <div className="flex gap-2">
                <Select
                  classNames={{
                    trigger: "dark:bg-slate-900 text-xs",
                    value: "dark:bg-slate-900 text-xs",
                    innerWrapper: "dark:bg-slate-900 text-xs",
                    label: "text-xs",
                  }}
                  variant="flat"
                  aria-label="Select Model"
                  selectedKeys={[selectedModel]}
                  className="w-[150px]"
                  onChange={(e) => setSelectedModel(e.target.value)}
                  size="sm"
                >
                  <SelectItem key="gemini">Gemini</SelectItem>
                  <SelectItem key="anthropic">Claude</SelectItem>
                </Select>
                {selectedFiles.length > 0 && (
                  <Select
                    classNames={{
                      trigger: "dark:bg-slate-900 text-xs",
                      value: "dark:bg-slate-900 text-xs",
                      innerWrapper: "dark:bg-slate-900 text-xs",
                      label: "text-xs",
                    }}
                    variant="flat"
                    aria-label="Processing Method"
                    selectedKeys={[processingMethod]}
                    className="w-[150px]"
                    onChange={(e) => setProcessingMethod(e.target.value as "multimodal" | "text_extraction")}
                    size="sm"
                  >
                    <SelectItem key="multimodal">Image Analysis</SelectItem>
                    <SelectItem key="text_extraction">Text Extraction</SelectItem>
                  </Select>
                )}
                <button
                  className={cn(
                    "text-xs px-3 rounded-3xl font-medium transition-all duration-200 flex items-center gap-1.5 border",
                    isRavidSearchActive
                      ? "dark:bg-[#1f1729] bg-[#b790ca] text-white border-[#7d5191]"
                      : "border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-slate-700"
                  )}
                  onClick={activateRavidSearch}
                >
                  <RsIcon className="w-4 h-4" />
                  R.A.V.I.D. Search
                </button>
              </div>
              <div className="absolute right-3 bottom-1 flex items-center gap-2">
                <input type="file" ref={fileInputRef} onChange={handleFileSelect} className="hidden" multiple accept="image/*,.pdf,.doc,.docx,.txt" />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="h-6 w-6 rounded-full p-1 transition-all duration-300 cursor-pointer text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700"
                >
                  <ImageIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={isListening ? stopListening : startListening}
                  className={cn(
                    "h-6 w-6 rounded-full p-1 transition-all duration-300 cursor-pointer hover:scale-125",
                    isListening ? "text-red-500 hover:text-red-600" : "text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700"
                  )}
                >
                  <Mic className="h-full w-full transition-all hover:text-gray-700 dark:hover:text-gray-200 text-white-500 dark:text-gray-400" />
                </button>
                <DownloadAsPdf
                  text={formatChatForPDF()}
                  className={cn(
                    "h-6 w-6 rounded-full border-none p-1 transition-all duration-300 hover:scale-125",
                    messages.length === 0 ? "opacity-50 cursor-not-allowed" : ""
                  )}
                />
                <Button
                  onPress={handleSendMessage}
                  isDisabled={isStarting || isSending}
                  size="sm"
                  className={`bg-[#7b3304] text-white hover:bg-[#833403] disabled:opacity-50 disabled:cursor-not-allowed rounded-lg flex items-center justify-center min-w-10 ${
                    inputMessage.trim() ? "bg-[#7b3304] text-white hover:bg-[#833403]" : "bg-gray-200 dark:bg-slate-700"
                  }`}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
