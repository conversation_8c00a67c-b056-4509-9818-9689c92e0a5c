# Build stage - Use Bun's official image for optimal performance
FROM oven/bun:1.1.42-alpine AS builder
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest

# Copy package files for dependency installation
COPY package.json bun.lockb ./

# Install dependencies using bun (faster than npm)
# --frozen-lockfile ensures reproducible builds
# Include devDependencies for build (default behavior)
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN bun run build

# Production stage - Use minimal Node.js Alpine for smaller final image
FROM node:20-alpine AS runner
WORKDIR /app

# Add build argument for git commit
ARG GIT_COMMIT=latest
# Add label with git commit for traceability
LABEL git_commit=$GIT_COMMIT

# Set production environment
ENV NODE_ENV=production

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy built application from builder stage
# With standalone output, Next.js creates a minimal server with all dependencies
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Switch to non-root user
USER nextjs

EXPOSE 3000

# Use Node.js to run the standalone server (bun not needed for standalone)
CMD ["node", "server.js"]

